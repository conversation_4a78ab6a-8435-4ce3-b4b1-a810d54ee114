import * as React from "react"
import { Sparkles } from "lucide-react"
import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

export interface EnhancedTextareaProps extends TextareaProps {
  onEnhance?: (value: string) => void;
  isEnhancing?: boolean;
  showEnhanceButton?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

const EnhancedTextarea = React.forwardRef<HTMLTextAreaElement, EnhancedTextareaProps>(
  ({ className, onEnhance, isEnhancing = false, showEnhanceButton = true, value, ...props }, ref) => {
    const handleEnhanceClick = () => {
      if (onEnhance && value && typeof value === 'string' && value.trim()) {
        onEnhance(value.trim());
      }
    };

    const shouldShowButton = showEnhanceButton && value && typeof value === 'string' && value.trim().length > 0;

    return (
      <div className="relative">
        <textarea
          className={cn(
            "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            shouldShowButton && "pr-10 sm:pr-12", // Responsive right padding when button is visible
            className
          )}
          ref={ref}
          value={value}
          {...props}
        />
        {shouldShowButton && (
          <div className="absolute right-2 top-2 sm:right-3 sm:top-3 animate-in fade-in-0 slide-in-from-right-2 duration-200">
            <button
              type="button"
              onClick={handleEnhanceClick}
              disabled={isEnhancing}
              className={cn(
                "group relative p-1.5 sm:p-2 rounded-lg transition-all duration-200 transform",
                "text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300",
                "hover:bg-gray-100 dark:hover:bg-gray-800 hover:shadow-md",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "active:scale-95",
                isEnhancing && "bg-blue-50 dark:bg-blue-900/20 shadow-sm"
              )}
              aria-label="Enhance SaaS idea with AI"
              title={isEnhancing ? "Enhancing..." : "Enhance with AI"}
            >
              <Sparkles
                className={cn(
                  "w-4 h-4 sm:w-5 sm:h-5 transition-all duration-200",
                  isEnhancing ? "animate-pulse text-blue-500" : "group-hover:text-blue-500",
                  "group-hover:scale-110 group-active:scale-95"
                )}
              />
              {isEnhancing && (
                <div className="absolute inset-0 rounded-lg border-2 border-blue-200 animate-ping opacity-75" />
              )}
            </button>
            {/* Enhanced tooltip */}
            <div className={cn(
              "absolute bottom-full right-0 mb-2 px-3 py-1.5 text-xs font-medium text-white bg-gray-900 dark:bg-gray-700 rounded-md shadow-lg",
              "opacity-0 pointer-events-none transition-all duration-200 transform translate-y-1",
              "group-hover:opacity-100 group-hover:translate-y-0 whitespace-nowrap z-20",
              "before:content-[''] before:absolute before:top-full before:right-3 before:border-4 before:border-transparent before:border-t-gray-900 dark:before:border-t-gray-700"
            )}>
              {isEnhancing ? "✨ Enhancing..." : "✨ Enhance with AI"}
            </div>
          </div>
        )}
      </div>
    )
  }
)
EnhancedTextarea.displayName = "EnhancedTextarea"

export { Textarea, EnhancedTextarea }
