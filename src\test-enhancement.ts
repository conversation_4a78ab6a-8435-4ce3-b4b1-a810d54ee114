// Simple test script to verify the AI enhancement functionality
import { enhanceSaasIdea } from './lib/aiEnhancer';

const testEnhancement = async () => {
  console.log('Testing AI Enhancement Feature...');
  
  const testInput = "A platform for managing team tasks and projects";
  
  try {
    const result = await enhanceSaasIdea(testInput);
    
    console.log('Original input:', testInput);
    console.log('Enhancement result:', result);
    console.log('Success:', result.success);
    console.log('Enhanced text length:', result.enhanced.length);
    console.log('Enhanced text preview:', result.enhanced.substring(0, 200) + '...');
    
    if (result.error) {
      console.log('Error (using fallback):', result.error);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Export for potential use in browser console
(window as any).testEnhancement = testEnhancement;

export { testEnhancement };
